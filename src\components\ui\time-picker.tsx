import React, { useState, useEffect, useRef } from "react";
import { Clock, Keyboard } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface TimePickerProps {
  value: string;
  onChange: (time: string) => void;
  className?: string;
  disabled?: boolean;
}

const TimePicker: React.FC<TimePickerProps> = ({
  value,
  onChange,
  className,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [hours, setHours] = useState<number>(7);
  const [minutes, setMinutes] = useState<number>(0);
  const [period, setPeriod] = useState<"AM" | "PM">("AM");
  const [mode, setMode] = useState<"hours" | "minutes">("hours");
  const inputRef = useRef<HTMLInputElement>(null);
  const clockRef = useRef<SVGSVGElement>(null);

  // Parse the input value when it changes
  useEffect(() => {
    if (value) {
      const [timeStr] = value.split(":");
      const hour = parseInt(timeStr, 10);
      
      if (hour >= 12) {
        setHours(hour === 12 ? 12 : hour - 12);
        setPeriod("PM");
      } else {
        setHours(hour === 0 ? 12 : hour);
        setPeriod("AM");
      }
      
      const [, minuteStr] = value.split(":");
      setMinutes(parseInt(minuteStr, 10) || 0);
    }
  }, [value]);

  // Update the time when hours, minutes, or period changes
  const updateTime = () => {
    let hour = hours;
    
    // Convert to 24-hour format
    if (period === "PM" && hours < 12) {
      hour += 12;
    } else if (period === "AM" && hours === 12) {
      hour = 0;
    }
    
    const formattedHours = hour.toString().padStart(2, "0");
    const formattedMinutes = minutes.toString().padStart(2, "0");
    const formattedTime = `${formattedHours}:${formattedMinutes}`;
    
    onChange(formattedTime);
  };

  // Handle hour selection
  const handleHourSelect = (hour: number) => {
    setHours(hour);
    setMode("minutes");
  };

  // Handle minute selection
  const handleMinuteSelect = (minute: number) => {
    setMinutes(minute);
  };

  // Handle period change (AM/PM)
  const handlePeriodChange = (newPeriod: "AM" | "PM") => {
    setPeriod(newPeriod);
  };

  // Handle clock face click
  const handleClockClick = (e: React.MouseEvent<SVGSVGElement>) => {
    if (!clockRef.current) return;
    
    const rect = clockRef.current.getBoundingClientRect();
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Calculate angle in radians
    const angle = Math.atan2(y - centerY, x - centerX);
    
    // Convert to degrees (0-360)
    let degrees = angle * (180 / Math.PI) + 90;
    if (degrees < 0) degrees += 360;
    
    if (mode === "hours") {
      // Convert degrees to hour (1-12)
      const hour = Math.round(degrees / 30) % 12;
      handleHourSelect(hour === 0 ? 12 : hour);
    } else {
      // Convert degrees to minute (0-55, step 5)
      const minute = Math.round(degrees / 6) % 60;
      handleMinuteSelect(minute);
    }
  };

  // Format display time
  const formatDisplayTime = () => {
    const formattedHours = hours.toString().padStart(2, "0");
    const formattedMinutes = minutes.toString().padStart(2, "0");
    return `${formattedHours}:${formattedMinutes}`;
  };

  // Generate clock numbers
  const clockNumbers = mode === "hours" 
    ? [12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
    : [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55];

  // Calculate positions for clock numbers
  const getNumberPosition = (index: number) => {
    const angle = (index * 30) * (Math.PI / 180);
    const radius = 80; // Distance from center
    const x = 100 + radius * Math.sin(angle);
    const y = 100 - radius * Math.cos(angle);
    return { x, y };
  };

  // Calculate hand position
  const getHandPosition = () => {
    const value = mode === "hours" ? hours : minutes / 5;
    const angle = ((value % 12) * 30) * (Math.PI / 180);
    const radius = 60; // Length of hand
    const x = 100 + radius * Math.sin(angle);
    const y = 100 - radius * Math.cos(angle);
    return { x, y };
  };

  // Handle OK button click
  const handleOk = () => {
    updateTime();
    setIsOpen(false);
  };

  // Handle Cancel button click
  const handleCancel = () => {
    setIsOpen(false);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <div className={cn("relative", className)}>
          <div 
            className={cn(
              "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
              "cursor-pointer",
              className
            )}
            onClick={() => !disabled && setIsOpen(true)}
          >
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <span>{formatDisplayTime()} {period}</span>
                <Clock className="h-4 w-4 ml-2 text-muted-foreground" />
              </div>
            </div>
          </div>
          <input
            ref={inputRef}
            type="time"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className="sr-only"
            disabled={disabled}
          />
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="center">
        <div className="flex flex-col bg-[#f5f5f0] rounded-md p-4">
          <div className="text-sm text-gray-500 mb-2">Select time</div>
          
          {/* Digital time display */}
          <div className="flex items-center justify-between mb-4">
            <div 
              className={cn(
                "flex items-center justify-center text-3xl font-bold w-16 h-16 rounded-md cursor-pointer",
                mode === "hours" ? "bg-green-200" : "bg-gray-100"
              )}
              onClick={() => setMode("hours")}
            >
              {hours.toString().padStart(2, "0")}
            </div>
            <div className="text-3xl font-bold">:</div>
            <div 
              className={cn(
                "flex items-center justify-center text-3xl font-bold w-16 h-16 rounded-md cursor-pointer",
                mode === "minutes" ? "bg-green-200" : "bg-gray-100"
              )}
              onClick={() => setMode("minutes")}
            >
              {minutes.toString().padStart(2, "0")}
            </div>
            
            {/* AM/PM toggle */}
            <div className="flex flex-col ml-2 border rounded-md overflow-hidden">
              <button
                className={cn(
                  "px-3 py-1 text-sm",
                  period === "AM" ? "bg-blue-100" : "bg-gray-100"
                )}
                onClick={() => handlePeriodChange("AM")}
              >
                AM
              </button>
              <button
                className={cn(
                  "px-3 py-1 text-sm",
                  period === "PM" ? "bg-blue-100" : "bg-gray-100"
                )}
                onClick={() => handlePeriodChange("PM")}
              >
                PM
              </button>
            </div>
          </div>
          
          {/* Clock face */}
          <div className="relative w-[200px] h-[200px] mx-auto mb-4 bg-gray-100 rounded-full">
            <svg 
              ref={clockRef}
              width="200" 
              height="200" 
              viewBox="0 0 200 200" 
              onClick={handleClockClick}
              className="cursor-pointer"
            >
              {/* Clock numbers */}
              {clockNumbers.map((number, index) => {
                const { x, y } = getNumberPosition(index);
                return (
                  <text
                    key={index}
                    x={x}
                    y={y}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    fontSize="12"
                    fill={
                      (mode === "hours" && number === hours) || 
                      (mode === "minutes" && number === minutes) 
                        ? "green" 
                        : "black"
                    }
                  >
                    {number}
                  </text>
                );
              })}
              
              {/* Clock center */}
              <circle cx="100" cy="100" r="4" fill="green" />
              
              {/* Clock hand */}
              <line
                x1="100"
                y1="100"
                x2={getHandPosition().x}
                y2={getHandPosition().y}
                stroke="green"
                strokeWidth="2"
              />
              
              {/* Clock hand circle */}
              <circle 
                cx={getHandPosition().x} 
                cy={getHandPosition().y} 
                r="16" 
                fill="green" 
                opacity="0.8"
              />
              <text
                x={getHandPosition().x}
                y={getHandPosition().y}
                textAnchor="middle"
                dominantBaseline="middle"
                fontSize="12"
                fill="white"
                fontWeight="bold"
              >
                {mode === "hours" ? hours : minutes}
              </text>
            </svg>
          </div>
          
          {/* Bottom buttons */}
          <div className="flex justify-between items-center">
            <button className="flex items-center text-gray-500">
              <Keyboard className="h-5 w-5" />
            </button>
            <div>
              <button 
                className="px-4 py-1 text-green-600 mr-2"
                onClick={handleCancel}
              >
                Cancel
              </button>
              <button 
                className="px-4 py-1 text-green-600"
                onClick={handleOk}
              >
                OK
              </button>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export { TimePicker };
