import React, { useState } from "react";
import { X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import IncidentReportForm from "./IncidentReportForm";

interface NewIncidentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onIncidentCreated?: (incident: any) => void;
}

const NewIncidentDialog: React.FC<NewIncidentDialogProps> = ({
  open,
  onOpenChange,
  onIncidentCreated,
}) => {
  const [navigation, setNavigation] = useState<{
    canGoNext: boolean;
    canGoPrevious: boolean;
    currentStep: number;
    totalSteps: number;
    onNext: () => void;
    onPrevious: () => void;
    onSubmit: () => void;
    isLastStep: boolean;
  } | null>(null);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl w-[90vw] max-h-[90vh] flex flex-col p-0">
        <DialogClose className="absolute right-4 top-4 z-10 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>

        {/* Fixed Header */}
        <DialogHeader className="flex-shrink-0 p-6 pb-4 border-b bg-white">
          <DialogTitle className="text-2xl font-bold text-gray-900">Report an Incident</DialogTitle>
          <DialogDescription className="text-gray-600 mt-2">
            Please complete this form to report any incidents or near misses.
            {navigation && (
              <span className="block mt-1 text-sm text-gray-500">
                Step {navigation.currentStep} of {navigation.totalSteps}
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto px-6 py-4">
          <IncidentReportForm
            onSuccess={(incidentData) => {
              if (incidentData && onIncidentCreated) {
                onIncidentCreated(incidentData);
              }
              onOpenChange(false);
            }}
            isDialog={true}
            onNavigationChange={setNavigation}
          />
        </div>

        {/* Fixed Footer */}
        {navigation && (
          <DialogFooter className="flex-shrink-0 p-6 pt-4 border-t bg-gray-50">
            <div className="flex justify-between w-full">
              {navigation.canGoPrevious ? (
                <Button
                  type="button"
                  variant="outline"
                  onClick={navigation.onPrevious}
                >
                  Previous
                </Button>
              ) : (
                <div></div>
              )}

              {navigation.isLastStep ? (
                <Button
                  type="button"
                  onClick={navigation.onSubmit}
                  className="bg-primary hover:bg-primary/90"
                >
                  Initiate Report
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={navigation.onNext}
                  className="bg-primary hover:bg-primary/90"
                >
                  Next
                </Button>
              )}
            </div>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default NewIncidentDialog;
