// This file contains fixes for Shadcn UI components that might have issues with client-side rendering

// Fix for Select component
if (typeof window !== 'undefined') {
  // Define select function in the global scope if it doesn't exist
  if (!(window as any).select) {
    (window as any).select = function(selector: string) {
      return document.querySelector(selector);
    };
  }
}

// Fix for global scope in React components
// This ensures that 'select' is available in the global scope for all components
if (typeof globalThis !== 'undefined' && !(globalThis as any).select) {
  (globalThis as any).select = function(selector: string) {
    if (typeof document !== 'undefined') {
      return document.querySelector(selector);
    }
    return null;
  };
}

export {};
