import { useState, useRef, useEffect } from "react";
import { Upload, Eye, X, Image, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
  DialogFooter,
} from "@/components/ui/dialog";

interface AttachmentUploadProps {
  incidentId: string;
  existingAttachments?: File[];
  onAttachmentChange?: (attachments: File[]) => void;
}

const AttachmentUpload: React.FC<AttachmentUploadProps> = ({
  incidentId,
  existingAttachments = [],
  onAttachmentChange,
}) => {
  const [attachments, setAttachments] = useState<File[]>(existingAttachments);
  const [previews, setPreviews] = useState<string[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [selectedPreview, setSelectedPreview] = useState<string | null>(null);
  const [selectedPreviewIndex, setSelectedPreviewIndex] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize previews from existing attachments
  useEffect(() => {
    // Clear existing previews to avoid duplicates
    previews.forEach(preview => URL.revokeObjectURL(preview));

    if (existingAttachments && existingAttachments.length > 0) {
      setAttachments(existingAttachments);

      // Generate previews for existing attachments
      const newPreviews = existingAttachments.map(file => {
        // If the file already has a preview URL (e.g., from a previous render)
        if (file.name.startsWith('blob:')) {
          return file.name;
        }
        return URL.createObjectURL(file);
      });

      setPreviews(newPreviews);
    }
  }, [existingAttachments]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);

      if (attachments.length + newFiles.length > 5) {
        toast.error("Maximum 5 attachments allowed");
        return;
      }

      const updatedAttachments = [...attachments, ...newFiles];
      setAttachments(updatedAttachments);

      // Generate previews
      const newPreviews = newFiles.map(file => URL.createObjectURL(file));
      setPreviews(prev => [...prev, ...newPreviews]);

      // Notify parent component if callback exists
      if (onAttachmentChange) {
        onAttachmentChange(updatedAttachments);
      }

      toast.success("Attachment uploaded successfully");
    }
  };

  const openPreview = (previewUrl: string, index: number) => {
    setSelectedPreview(previewUrl);
    setSelectedPreviewIndex(index);
    setPreviewOpen(true);
  };

  const deleteAttachment = (index: number) => {
    // Create new arrays without the deleted attachment
    const updatedAttachments = attachments.filter((_, i) => i !== index);

    // Revoke the URL to avoid memory leaks
    URL.revokeObjectURL(previews[index]);
    const updatedPreviews = previews.filter((_, i) => i !== index);

    // Update state
    setAttachments(updatedAttachments);
    setPreviews(updatedPreviews);

    // If we're deleting the currently selected preview, select another one or close
    if (selectedPreviewIndex === index) {
      if (updatedPreviews.length > 0) {
        const newIndex = index > 0 ? index - 1 : 0;
        setSelectedPreview(updatedPreviews[newIndex]);
        setSelectedPreviewIndex(newIndex);
      } else {
        setPreviewOpen(false);
      }
    } else if (selectedPreviewIndex > index) {
      // If we're deleting an attachment before the selected one, adjust the index
      setSelectedPreviewIndex(selectedPreviewIndex - 1);
    }

    // Notify parent component
    if (onAttachmentChange) {
      onAttachmentChange(updatedAttachments);
    }

    toast.success("Attachment deleted successfully");
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Upload Button */}
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8 rounded-full hover:bg-primary/10"
        onClick={triggerFileInput}
        title="Upload Attachment"
      >
        <Upload className="h-4 w-4" />
      </Button>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        hidden
        accept="image/*"
        onChange={handleFileChange}
      />

      {/* View Button - Always show if there are attachments */}
      {previews.length > 0 && (
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 rounded-full hover:bg-primary/10"
          onClick={() => openPreview(previews[0], 0)}
          title="View Attachment"
        >
          <Eye className="h-4 w-4" />
        </Button>
      )}

      {/* Attachment Count Badge */}
      {attachments.length > 0 && (
        <span className="inline-flex items-center justify-center w-5 h-5 rounded-full text-xs font-medium bg-primary/10 text-primary">
          {attachments.length}
        </span>
      )}

      {/* Preview Dialog */}
      <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Attachment Preview</DialogTitle>
            <DialogClose />
          </DialogHeader>

          {selectedPreview && (
            <div className="flex justify-center relative">
              <img
                src={selectedPreview}
                alt="Attachment preview"
                className="max-h-[70vh] object-contain rounded-md"
              />
            </div>
          )}

          {/* Thumbnails of all attachments */}
          {previews.length > 0 && (
            <div className="grid grid-cols-5 gap-2 mt-4">
              {previews.map((preview, index) => (
                <div
                  key={index}
                  className={`relative cursor-pointer border-2 rounded-md overflow-hidden ${
                    index === selectedPreviewIndex ? 'border-primary' : 'border-transparent'
                  }`}
                >
                  <img
                    src={preview}
                    alt={`Thumbnail ${index + 1}`}
                    className="w-full h-16 object-cover"
                    onClick={() => {
                      setSelectedPreview(preview);
                      setSelectedPreviewIndex(index);
                    }}
                  />
                  <Button
                    variant="destructive"
                    size="icon"
                    className="absolute top-0 right-0 h-5 w-5 rounded-full bg-red-500 hover:bg-red-600 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteAttachment(index);
                    }}
                    title="Delete Attachment"
                  >
                    <X className="h-3 w-3 text-white" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          {/* Dialog footer with Delete Attachment button removed */}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AttachmentUpload;
