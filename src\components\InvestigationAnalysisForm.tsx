import React, { useState } from "react";
import { format } from "date-fns";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Plus, Trash2, Upload } from "lucide-react";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import HumanBodyDiagram from "./HumanBodyDiagram";

// Define the validation schema for the investigation form
const investigationFormSchema = z.object({
  // Incident Details
  incidentLocation: z.string().min(1, "Incident location is required"),
  locationPlanPhotos: z.array(z.any()).optional(),

  // People Involved
  peopleInjured: z.string().min(1, "Information about injured people is required"),
  witnesses: z.string().min(1, "Information about witnesses is required"),

  // Description of Incident
  howItHappened: z.string().min(1, "Description of how the incident happened is required"),
  equipmentInvolved: z.string().min(1, "Information about equipment involved is required"),
  activitiesPerformed: z.string().min(1, "Information about activities performed is required"),
  unusualConditions: z.string().min(1, "Information about unusual conditions is required"),
  safeWorkingProcedures: z.string().min(1, "Information about safe working procedures is required"),

  // Injury Details
  injurySustained: z.boolean().default(false),
  bodyPartsInjured: z.string().optional(),
  injuryDescription: z.string().optional(),
  firstAidAdministered: z.boolean().default(false),
  firstAidAdministeredBy: z.string().optional(),
  takenToMedicalFacility: z.boolean().default(false),
  medicalFacilityName: z.string().optional(),
  transportMode: z.array(z.string()).optional(),
  driverInfo: z.string().optional(),
  medicalTreatmentReceived: z.boolean().default(false),
  treatmentDescription: z.string().optional(),
  medicalLeaveGiven: z.boolean().default(false),
  medicalLeaveDays: z.number().optional(),

  // Return to Work Information
  regularWorkReturnDate: z.date().optional(),
  modifiedWorkReturnDate: z.date().optional(),
});

type InvestigationFormValues = z.infer<typeof investigationFormSchema>;

interface InvestigationAnalysisFormProps {
  incident: any;
  onSubmit: (data: InvestigationFormValues) => void;
  isReadOnly?: boolean;
  isNested?: boolean; // Add this prop to indicate if the form is nested inside another form
}

const InvestigationAnalysisForm: React.FC<InvestigationAnalysisFormProps> = ({
  incident,
  onSubmit,
  isReadOnly = false,
  isNested = false, // Default to false for backward compatibility
}) => {
  const [uploadedPhotos, setUploadedPhotos] = useState<File[]>([]);

  // Log the incident data for debugging
  console.log("InvestigationAnalysisForm - incident data:", incident);
  console.log("InvestigationAnalysisForm - incident.detailedInvestigation:", incident?.detailedInvestigation);
  console.log("InvestigationAnalysisForm - incident.investigationDetails:", incident?.investigationDetails);

  // Initialize form with existing data if available
  // First check if we have detailedInvestigation data, otherwise fall back to investigationDetails
  const defaultValues = incident?.detailedInvestigation ? {
    // Use detailedInvestigation data if available
    ...incident.detailedInvestigation
  } : {
    // Otherwise use investigationDetails data
    incidentLocation: incident?.investigationDetails?.incidentLocation || "",
    locationPlanPhotos: incident?.investigationDetails?.locationPlanPhotos || [],
    peopleInjured: incident?.investigationDetails?.peopleInjured || "",
    witnesses: incident?.investigationDetails?.witnesses || "",
    howItHappened: incident?.investigationDetails?.howItHappened || "",
    equipmentInvolved: incident?.investigationDetails?.equipmentInvolved || "",
    activitiesPerformed: incident?.investigationDetails?.activitiesPerformed || "",
    unusualConditions: incident?.investigationDetails?.unusualConditions || "",
    safeWorkingProcedures: incident?.investigationDetails?.safeWorkingProcedures || "",
    injurySustained: incident?.investigationDetails?.injurySustained || false,
    bodyPartsInjured: incident?.investigationDetails?.bodyPartsInjured || "",
    injuryDescription: incident?.investigationDetails?.injuryDescription || "",
    firstAidAdministered: incident?.investigationDetails?.firstAidAdministered || false,
    firstAidAdministeredBy: incident?.investigationDetails?.firstAidAdministeredBy || "",
    takenToMedicalFacility: incident?.investigationDetails?.takenToMedicalFacility || false,
    medicalFacilityName: incident?.investigationDetails?.medicalFacilityName || "",
    transportMode: incident?.investigationDetails?.transportMode || [],
    driverInfo: incident?.investigationDetails?.driverInfo || "",
    medicalTreatmentReceived: incident?.investigationDetails?.medicalTreatmentReceived || false,
    treatmentDescription: incident?.investigationDetails?.treatmentDescription || "",
    medicalLeaveGiven: incident?.investigationDetails?.medicalLeaveGiven || false,
    medicalLeaveDays: incident?.investigationDetails?.medicalLeaveDays || 0,
    regularWorkReturnDate: incident?.investigationDetails?.regularWorkReturnDate || undefined,
    modifiedWorkReturnDate: incident?.investigationDetails?.modifiedWorkReturnDate || undefined,
  };

  console.log("InvestigationAnalysisForm - defaultValues:", defaultValues);

  const form = useForm<InvestigationFormValues>({
    resolver: zodResolver(investigationFormSchema),
    mode: "onBlur", // Only validate on blur, not on change
    defaultValues,
  });

  const watchInjurySustained = form.watch("injurySustained");
  const watchFirstAidAdministered = form.watch("firstAidAdministered");
  const watchTakenToMedicalFacility = form.watch("takenToMedicalFacility");
  const watchMedicalTreatmentReceived = form.watch("medicalTreatmentReceived");
  const watchMedicalLeaveGiven = form.watch("medicalLeaveGiven");

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const newFiles = Array.from(event.target.files);
      setUploadedPhotos((prev) => [...prev, ...newFiles]);

      // Add to form
      const currentPhotos = form.getValues("locationPlanPhotos") || [];
      form.setValue("locationPlanPhotos", [...currentPhotos, ...newFiles]);
    }
  };

  const handleSubmit = (data: InvestigationFormValues) => {
    console.log("InvestigationAnalysisForm - handleSubmit called with data:", data);
    onSubmit(data);
  };

  // Styling classes
  const formLabelClass = "text-sm font-medium";
  const sectionTitleClass = "text-lg font-semibold mb-4";

  // Create the form content without the form tags
  const formContent = (
    <>
      {/* Incident Details Section */}
      <div className="space-y-4">
        <h2 className={sectionTitleClass}>1. Incident Details</h2>

          <FormField
            control={form.control}
            name="incidentLocation"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Where and when did the incident happen? (Required)</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Provide details about the location and time of the incident"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="space-y-2">
            <Label className={formLabelClass}>Upload location plan/sketch/pictures related to the incident:</Label>
            {!isReadOnly && (
              <div className="flex items-center gap-2">
                <Input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handlePhotoUpload}
                  className="flex-1"
                  disabled={isReadOnly}
                />
              </div>
            )}

            {/* Display uploaded photos */}
            {uploadedPhotos.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                {uploadedPhotos.map((photo, index) => (
                  <Card key={index} className="overflow-hidden">
                    <div className="aspect-square bg-gray-100 flex items-center justify-center">
                      <img
                        src={URL.createObjectURL(photo)}
                        alt={`Photo ${index + 1}`}
                        className="object-cover w-full h-full"
                      />
                    </div>
                    <CardContent className="p-3">
                      <p className="text-sm font-medium truncate">{photo.name}</p>
                      <p className="text-xs text-gray-500">{(photo.size / 1024).toFixed(2)} KB</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* People Involved Section */}
        <div className="space-y-4">
          <h2 className={sectionTitleClass}>2. People Involved</h2>

          <FormField
            control={form.control}
            name="peopleInjured"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Who was injured, suffered ill health, or was otherwise involved? (Required)</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Provide details about the people involved"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="witnesses"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Who witnessed the incident? (Required)</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Provide details about the witnesses"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Separator />

        {/* Description of Incident Section */}
        <div className="space-y-4">
          <h2 className={sectionTitleClass}>3. Description of Incident</h2>

          <FormField
            control={form.control}
            name="howItHappened"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>How did the incident happen? (Required)</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe how the incident occurred"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="equipmentInvolved"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>What equipment was involved? (Required)</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the equipment involved"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="activitiesPerformed"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>What activities were being performed at the time? (Required)</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the activities being performed"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="unusualConditions"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Were there any unusual or different working conditions? (Required)</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe any unusual working conditions"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="safeWorkingProcedures"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={formLabelClass}>Were there adequate safe working procedures, and were they followed? (Required)</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the safe working procedures and whether they were followed"
                    className="min-h-[100px]"
                    disabled={isReadOnly}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Separator />

        {/* Injury Details Section */}
        <div className="space-y-4">
          <h2 className={sectionTitleClass}>Injury Details</h2>

          <FormField
            control={form.control}
            name="injurySustained"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Were injuries sustained?</FormLabel>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isReadOnly}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {watchInjurySustained && (
            <div className="space-y-4 pl-4 border-l-2 border-primary/20">
              <div className="space-y-4">
                <h3 className="text-md font-medium">Parts of Body Injured:</h3>
                <p className="text-sm text-gray-500">Please indicate injury locations and describe injury types.</p>

                <div className="bg-gray-50 p-3 rounded-md mb-6">
                  <h3 className="text-sm font-medium mb-2">Body Injury Diagram</h3>
                  <div className="bg-white rounded-md">
                    <HumanBodyDiagram
                      value={form.getValues("bodyPartsInjured")?.split(',').filter(Boolean) || []}
                      onChange={(selectedParts) => {
                        form.setValue("bodyPartsInjured", selectedParts.join(','), { shouldValidate: true });
                      }}
                      readOnly={isReadOnly}
                    />
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="injuryDescription"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className={formLabelClass}>Injury Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe the injuries in detail"
                          className="min-h-[100px]"
                          disabled={isReadOnly}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="firstAidAdministered"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Was first aid administered on site?</FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isReadOnly}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {watchFirstAidAdministered && (
                  <FormField
                    control={form.control}
                    name="firstAidAdministeredBy"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className={formLabelClass}>Administered By:</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Name of person who administered first aid"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="takenToMedicalFacility"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Was the injured person taken to an offsite medical facility?</FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isReadOnly}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {watchTakenToMedicalFacility && (
                  <div className="space-y-4 pl-4 border-l-2 border-primary/20">
                    <FormField
                      control={form.control}
                      name="medicalFacilityName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className={formLabelClass}>Medical Facility Name:</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Name of the medical facility"
                              disabled={isReadOnly}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="space-y-2">
                      <Label className={formLabelClass}>Mode of Transport:</Label>
                      <div className="flex flex-wrap gap-4">
                        {["Ambulance", "Company Vehicle", "Private Vehicle"].map((option) => (
                          <div key={option} className="flex items-center space-x-2">
                            <Checkbox
                              id={`transport-${option}`}
                              checked={form.getValues("transportMode")?.includes(option)}
                              onCheckedChange={(checked) => {
                                const currentModes = form.getValues("transportMode") || [];
                                if (checked) {
                                  form.setValue("transportMode", [...currentModes, option]);
                                } else {
                                  form.setValue(
                                    "transportMode",
                                    currentModes.filter((mode) => mode !== option)
                                  );
                                }
                              }}
                              disabled={isReadOnly}
                            />
                            <label
                              htmlFor={`transport-${option}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {option}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="driverInfo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className={formLabelClass}>Name/Role of Driver:</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Name and role of the driver"
                              disabled={isReadOnly}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}

                <FormField
                  control={form.control}
                  name="medicalTreatmentReceived"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Was medical treatment received?</FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isReadOnly}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {watchMedicalTreatmentReceived && (
                  <FormField
                    control={form.control}
                    name="treatmentDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className={formLabelClass}>Brief Description of the Treatment:</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe the medical treatment received"
                            className="min-h-[100px]"
                            disabled={isReadOnly}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="medicalLeaveGiven"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Was medical leave given?</FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isReadOnly}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {watchMedicalLeaveGiven && (
                  <FormField
                    control={form.control}
                    name="medicalLeaveDays"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className={formLabelClass}>Number of Days:</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={0}
                            placeholder="Number of days of medical leave"
                            disabled={isReadOnly}
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>

              <div className="space-y-4">
                <h3 className="text-md font-medium">Return to Work Information</h3>

                <FormField
                  control={form.control}
                  name="regularWorkReturnDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className={formLabelClass}>Date of Return to Regular Work:</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                              disabled={isReadOnly}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="modifiedWorkReturnDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className={formLabelClass}>Date of Return to Modified Work (if applicable):</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                              disabled={isReadOnly}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}
        </div>

        {!isReadOnly && !isNested && (
          <div className="flex justify-end">
            <Button type="submit" className="bg-primary hover:bg-primary/90">
              Save Investigation Details
            </Button>
          </div>
        )}
    </>
  );

  // Add event listeners to all form fields to trigger form submission
  React.useEffect(() => {
    // Subscribe to form changes
    const subscription = form.watch(() => {
      console.log("InvestigationAnalysisForm - form changed");
      // Get the current form values and submit them to the parent component
      const currentData = form.getValues();
      console.log("InvestigationAnalysisForm - current form values:", currentData);
      onSubmit(currentData);
    });

    // Cleanup subscription on unmount
    return () => subscription.unsubscribe();
  }, [form, onSubmit]);

  // Return different structures based on whether this is nested in another form
  return isNested ? (
    <div className="space-y-8">
      {formContent}
    </div>
  ) : (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        {formContent}
      </form>
    </Form>
  );
};

export default InvestigationAnalysisForm;
