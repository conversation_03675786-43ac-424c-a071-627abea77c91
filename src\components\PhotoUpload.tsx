
import { useState, useRef } from "react";
import { Camera, Image, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface PhotoUploadProps {
  onPhotosChange: (photos: File[]) => void;
  existingPhotos?: File[];
}

const PhotoUpload: React.FC<PhotoUploadProps> = ({
  onPhotosChange,
  existingPhotos = []
}) => {
  const [photos, setPhotos] = useState<File[]>(existingPhotos);
  const [previews, setPreviews] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      
      if (photos.length + newFiles.length > 5) {
        toast.error("Maximum 5 photos allowed");
        return;
      }
      
      const updatedPhotos = [...photos, ...newFiles];
      setPhotos(updatedPhotos);
      onPhotosChange(updatedPhotos);
      
      // Generate previews
      const newPreviews = newFiles.map(file => URL.createObjectURL(file));
      setPreviews(prev => [...prev, ...newPreviews]);
    }
  };

  const removePhoto = (index: number) => {
    const updatedPhotos = photos.filter((_, i) => i !== index);
    setPhotos(updatedPhotos);
    onPhotosChange(updatedPhotos);
    
    // Revoke the URL to avoid memory leaks
    URL.revokeObjectURL(previews[index]);
    setPreviews(prev => prev.filter((_, i) => i !== index));
  };

  const triggerFileInput = (source: 'camera' | 'library') => {
    if (fileInputRef.current) {
      fileInputRef.current.accept = source === 'camera' 
        ? 'image/*;capture=camera' 
        : 'image/*';
      fileInputRef.current.click();
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-3">
        <Button 
          type="button" 
          onClick={() => triggerFileInput('camera')}
          className="flex items-center gap-2"
        >
          <Camera size={18} />
          Take Photo
        </Button>
        <Button 
          type="button" 
          variant="outline"
          onClick={() => triggerFileInput('library')}
          className="flex items-center gap-2"
        >
          <Image size={18} />
          Choose from Library
        </Button>
        <input
          ref={fileInputRef}
          type="file"
          hidden
          accept="image/*"
          onChange={handleFileChange}
          multiple
        />
      </div>
      
      {previews.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 mt-4">
          {previews.map((preview, index) => (
            <div key={index} className="relative">
              <img
                src={preview}
                alt={`Incident photo ${index + 1}`}
                className="w-full h-48 object-cover rounded-md"
              />
              <button
                type="button"
                onClick={() => removePhoto(index)}
                className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-md"
              >
                <X size={16} className="text-red-500" />
              </button>
            </div>
          ))}
        </div>
      )}
      <p className="text-xs text-gray-500">
        Upload up to 5 photos. Clear images help with incident investigation.
      </p>
    </div>
  );
};

export default PhotoUpload;
