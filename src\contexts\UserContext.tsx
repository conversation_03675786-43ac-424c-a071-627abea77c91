import React, { createContext, useContext, useState, ReactNode } from 'react';

// Define the available user roles
export type UserRole = 'reporter' | 'reviewer';

// Define the user context type
interface UserContextType {
  role: UserRole;
  setRole: (role: UserRole) => void;
  userName: string;
  userTitle: string;
}

// Create the context with default values
const UserContext = createContext<UserContextType>({
  role: 'reporter',
  setRole: () => {},
  userName: 'Safety Reporter',
  userTitle: 'Safety Officer',
});

// Create a provider component
interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [role, setRole] = useState<UserRole>('reporter');

  // Get user title based on role
  const getUserTitle = (userRole: UserRole): string => {
    switch (userRole) {
      case 'reporter':
        return 'Safety Officer';
      case 'reviewer':
        return 'Safety Manager';
      default:
        return 'Employee';
    }
  };

  // Get user name based on role
  const getUserName = (userRole: UserRole): string => {
    switch (userRole) {
      case 'reporter':
        return 'Safety Reporter';
      case 'reviewer':
        return 'Safety Reviewer';
      default:
        return 'User';
    }
  };

  const userName = getUserName(role);
  const userTitle = getUserTitle(role);

  return (
    <UserContext.Provider value={{ role, setRole, userName, userTitle }}>
      {children}
    </UserContext.Provider>
  );
};

// Create a hook to use the user context
export const useUser = () => useContext(UserContext);
