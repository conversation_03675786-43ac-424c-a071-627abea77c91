import React, { useState, useEffect } from 'react';
import Model, { IExerciseData, IMuscleStats } from 'react-body-highlighter';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface HumanBodyDiagramProps {
  value: string[];
  onChange: (value: string[]) => void;
  readOnly?: boolean;
}

const HumanBodyDiagram: React.FC<HumanBodyDiagramProps> = ({
  value = [],
  onChange,
  readOnly = false,
}) => {
  const [selectedMuscles, setSelectedMuscles] = useState<string[]>(value);
  const [viewType, setViewType] = useState<'anterior' | 'posterior'>('anterior');

  // Update the parent component when selected muscles change
  useEffect(() => {
    onChange(selectedMuscles);
  }, [selectedMuscles, onChange]);

  // Update local state when value prop changes
  useEffect(() => {
    setSelectedMuscles(value);
  }, [value]);

  // Convert selected muscles to the format expected by react-body-highlighter
  const data: IExerciseData[] = selectedMuscles.map(muscle => ({
    name: `Injury on ${muscle}`,
    muscles: [muscle],
  }));

  const handleClick = ({ muscle }: IMuscleStats) => {
    if (readOnly) return;

    setSelectedMuscles(prev => {
      // If muscle is already selected, remove it
      if (prev.includes(muscle)) {
        return prev.filter(m => m !== muscle);
      }
      // Otherwise, add it
      return [...prev, muscle];
    });
  };

  return (
    <div>
      {/* Main container with responsive layout */}
      <div className="grid grid-cols-1 md:grid-cols-12 gap-4" style={{ height: '600px' }}>
        {/* Left side with body diagram - takes more space on larger screens */}
        <div className="md:col-span-8 space-y-3">
          <div className="grid grid-cols-2 gap-1">
            <div
              className={`text-center py-2 px-4 cursor-pointer rounded-t-md border-t border-l border-r ${viewType === 'anterior' ? 'bg-white text-primary font-medium' : 'bg-gray-50 text-gray-500'}`}
              onClick={() => setViewType('anterior')}
            >
              Front View
            </div>
            <div
              className={`text-center py-2 px-4 cursor-pointer rounded-t-md border-t border-l border-r ${viewType === 'posterior' ? 'bg-white text-primary font-medium' : 'bg-gray-50 text-gray-500'}`}
              onClick={() => setViewType('posterior')}
            >
              Back View
            </div>
          </div>

          <div className="flex justify-center p-3 border rounded-md bg-white" style={{ height: 'calc(600px - 70px)' }}>
            <Model
              data={data}
              type={viewType}
              onClick={handleClick}
              style={{ width: '100%', maxWidth: '450px', height: 'auto' }} /* Further increased size */
              bodyColor="#e2e8f0" // Light gray color for unselected muscles
              highlightedColors={['#ef4444']} // Red color for selected muscles (injuries)
            />
          </div>
        </div>

        {/* Right side with selected areas */}
        <div className="md:col-span-4">
          <div className="border rounded-md bg-white flex flex-col" style={{ height: '600px' }}>
            <div className="p-3 border-b flex justify-between items-center">
              <h3 className="text-sm font-medium">Selected Areas</h3>
              <div className="text-gray-400">▲</div>
            </div>

            <div className="flex-1 flex flex-col">
              <div className="flex-1 overflow-y-auto" style={{ maxHeight: 'calc(600px - 100px)' }}>
                {selectedMuscles.length === 0 ? (
                  <div className="flex-1 flex items-center justify-center h-full">
                    <p className="text-xs text-slate-500 italic">No areas selected</p>
                  </div>
                ) : (
                  <div className="flex flex-col w-full">
                    {selectedMuscles.map(muscle => (
                      <div key={muscle} className="flex items-center justify-between px-4 py-3 border-b">
                        <span className="text-sm text-gray-800">
                          {muscle.replace(/-/g, ' ')
                            .split(' ')
                            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                            .join(' ')}
                        </span>
                        {!readOnly && (
                          <span
                            className="text-red-500 cursor-pointer text-lg"
                            onClick={() => setSelectedMuscles(prev => prev.filter(m => m !== muscle))}
                          >
                            ×
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="mt-auto">
                <div className="flex justify-center border-t border-b py-1">
                  <div className="text-gray-400">▼</div>
                </div>
                {!readOnly && (
                  <div className="p-3">
                    <button
                      type="button"
                      onClick={() => setSelectedMuscles([])}
                      className="w-full py-2 border rounded-md bg-white hover:bg-gray-50 text-sm"
                    >
                      Clear All
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HumanBodyDiagram;
