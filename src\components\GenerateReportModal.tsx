import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Search, FileText, AlertCircle, Loader2 } from 'lucide-react';
import { Incident } from '@/contexts/IncidentContext';
import { format } from 'date-fns';
import IncidentReportPDF from './IncidentReportPDF';
import { generatePdfFromElement, getReportFilename } from '@/utils/pdfUtils';
import { toast } from 'sonner';

interface GenerateReportModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  incidents: Incident[];
}

const GenerateReportModal: React.FC<GenerateReportModalProps> = ({
  open,
  onOpenChange,
  incidents,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const reportRef = useRef<HTMLDivElement>(null);

  // Filter closed incidents
  const closedIncidents = incidents.filter(
    (incident) => incident.status === 'closed'
  );

  // Filter incidents based on search term
  const filteredIncidents = closedIncidents.filter(
    (incident) =>
      incident.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (incident.incidentTitle && incident.incidentTitle.toLowerCase().includes(searchTerm.toLowerCase())) ||
      incident.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle incident selection
  const handleSelectIncident = (incident: Incident) => {
    setSelectedIncident(incident);
  };

  // Handle PDF generation
  const handleGeneratePDF = async () => {
    if (!selectedIncident || !reportRef.current) return;

    try {
      setIsGenerating(true);
      
      // Generate the filename
      const filename = getReportFilename(selectedIncident);
      
      // Generate the PDF
      await generatePdfFromElement(reportRef.current, filename);
      
      // Show success message
      toast.success('PDF report generated successfully!', {
        description: `The report has been saved as ${filename}`,
      });
      
      // Close the modal
      onOpenChange(false);
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate PDF report', {
        description: 'An error occurred while generating the PDF. Please try again.',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Generate Incident Report
          </DialogTitle>
          <DialogDescription>
            Select a closed incident to generate a comprehensive PDF report.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col">
          {!selectedIncident ? (
            <>
              {/* Search and incident selection */}
              <div className="mb-4 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search by ID, title, or description..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <div className="flex-1 overflow-auto border rounded-md">
                {filteredIncidents.length > 0 ? (
                  <Table>
                    <TableHeader className="bg-muted/50 sticky top-0">
                      <TableRow>
                        <TableHead className="w-[100px]">ID</TableHead>
                        <TableHead className="w-[150px]">Date</TableHead>
                        <TableHead>Title</TableHead>
                        <TableHead className="w-[120px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredIncidents.map((incident) => (
                        <TableRow key={incident.id} className="hover:bg-muted/50">
                          <TableCell className="font-medium">{incident.id}</TableCell>
                          <TableCell>{format(incident.incidentDate, 'PPP')}</TableCell>
                          <TableCell>
                            <div className="max-w-[300px] truncate" title={incident.incidentTitle || incident.description}>
                              {incident.incidentTitle || incident.description}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSelectIncident(incident)}
                            >
                              Select
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="flex flex-col items-center justify-center h-full py-8 text-center text-gray-500">
                    <AlertCircle className="h-12 w-12 mb-2 text-gray-400" />
                    <p>No closed incidents found</p>
                    <p className="text-sm mt-2">
                      {closedIncidents.length === 0
                        ? "There are no incidents with 'Closed' status."
                        : "No incidents match your search criteria."}
                    </p>
                  </div>
                )}
              </div>
            </>
          ) : (
            // Report preview
            <div className="flex-1 overflow-auto border rounded-md bg-gray-100 p-4">
              <div className="bg-white rounded-md shadow-sm">
                <IncidentReportPDF ref={reportRef} incident={selectedIncident} />
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between items-center pt-4 border-t">
          {selectedIncident ? (
            <>
              <Button variant="outline" onClick={() => setSelectedIncident(null)}>
                Back to Selection
              </Button>
              <Button onClick={handleGeneratePDF} disabled={isGenerating}>
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating PDF...
                  </>
                ) : (
                  <>
                    <FileText className="mr-2 h-4 w-4" />
                    Generate PDF
                  </>
                )}
              </Button>
            </>
          ) : (
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default GenerateReportModal;
