// Mock data for incidents
export const mockIncidents = [
  {
    id: "INC-123456",
    incidentTitle: "Fall from Ladder",
    incidentDate: new Date("2025-04-15"),
    incidentTime: "14:30",
    incidentType: "fall",
    incidentCategory: "safety",
    description: "Worker fell from ladder while changing light bulb",

    // Location information
    locationCountry: "United States",
    locationCity: "Chicago",
    locationBusinessUnit: "Manufacturing",
    locationProject: "Factory A",
    locationDetails: "Building 3, Floor 2",

    // Classification
    isWorkRelated: true,
    lossOfConsciousness: false,
    isDangerousOccurrence: true,
    injuryClassification: {
      isFatality: false,
      isPermanentDisability: false,
      isLostTimeIncident: true,
      isMedicalTreatment: true,
      isFirstAid: true,
    },

    // Incident management
    reportedAt: new Date("2025-04-15"),
    reportedBy: "Safety Reporter",
    reviewedBy: "<PERSON>",
    requiresAction: true,
    status: "under-review",
    stage: "Supplementary information in Progress",
    investigationStatus: "not-started",

    // Additional details
    propertyDamage: false,
    propertyDamageDetails: "",
    riskCategories: ["fall", "equipment"],
    photos: [],
    attachments: [],

    // Investigation details
    investigationDetails: null
  },
  {
    id: "INC-789012",
    incidentTitle: "Chemical Spill",
    incidentDate: new Date("2025-04-10"),
    incidentTime: "09:15",
    incidentType: "chemical",
    incidentCategory: "environmental",
    description: "Small chemical spill in laboratory area",

    // Location information
    locationCountry: "United Kingdom",
    locationCity: "London",
    locationBusinessUnit: "Research",
    locationProject: "Lab B",
    locationDetails: "Chemistry Lab, Room 101",

    // Classification
    isWorkRelated: true,
    lossOfConsciousness: false,
    isDangerousOccurrence: false,
    injuryClassification: {
      isFatality: false,
      isPermanentDisability: false,
      isLostTimeIncident: false,
      isMedicalTreatment: false,
      isFirstAid: true,
    },

    // Incident management
    reportedAt: new Date("2025-04-10"),
    reportedBy: "Safety Reporter",
    reviewedBy: "Maria Rodriguez",
    requiresAction: false,
    status: "submitted",
    stage: "Preliminary Analysis in Progress",
    investigationStatus: "in-progress",

    // Additional details
    propertyDamage: true,
    propertyDamageDetails: "Damage to lab equipment",
    riskCategories: ["chemical", "environmental"],
    photos: [],
    attachments: [],

    // Investigation details
    investigationDetails: {
      rootCause: "Improper storage of chemicals",
      correctiveActions: "Update chemical storage procedures",
      preventiveMeasures: "Additional staff training",
    }
  },
  {
    id: "INC-345678",
    incidentTitle: "Near Miss - Vehicle",
    incidentDate: new Date("2025-04-05"),
    incidentTime: "16:45",
    incidentType: "nearMiss",
    incidentCategory: "safety",
    description: "Near miss incident involving forklift in warehouse",

    // Location information
    locationCountry: "Australia",
    locationCity: "Sydney",
    locationBusinessUnit: "Logistics",
    locationProject: "Warehouse C",
    locationDetails: "Loading Bay 2",

    // Classification
    isWorkRelated: true,
    lossOfConsciousness: false,
    isDangerousOccurrence: true,
    injuryClassification: {
      isFatality: false,
      isPermanentDisability: false,
      isLostTimeIncident: false,
      isMedicalTreatment: false,
      isFirstAid: false,
    },

    // Incident management
    reportedAt: new Date("2025-04-05"),
    reportedBy: "Safety Reporter",
    reviewedBy: "David Chen",
    requiresAction: false,
    status: "closed",
    stage: "Investigation Complete",
    investigationStatus: "completed",

    // Additional details
    propertyDamage: false,
    propertyDamageDetails: "",
    riskCategories: ["vehicle", "equipment"],
    photos: [],
    attachments: [],

    // Investigation details
    investigationDetails: {
      rootCause: "Inadequate visibility in loading area",
      correctiveActions: "Install additional mirrors and warning signs",
      preventiveMeasures: "Refresher training for forklift operators",
    }
  }
];

// Helper function to get status label
export const getStatusLabel = (status: string) => {
  switch (status) {
    case "open":
      return "Reviewed";
    case "under-review":
      return "Under Review";
    case "investigation":
      return "Under Investigation";
    case "closed":
      return "Closed";
    default:
      return status;
  }
};

// Helper function to get incident type label
export const getIncidentTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    "fall": "Fall",
    "slip": "Slip",
    "nearMiss": "Near Miss",
    "injury": "Injury",
    "environmental": "Environmental",
    "security": "Security",
    "fire": "Fire",
    "electrical": "Electrical",
    "chemical": "Chemical",
    "vehicle": "Vehicle",
    "other": "Other"
  };

  return typeMap[type] || type;
};

// Helper function to get incident category label
export const getIncidentCategoryLabel = (category: string) => {
  const categoryMap: Record<string, string> = {
    "safety": "Safety",
    "health": "Health",
    "environmental": "Environmental",
    "security": "Security",
    "electrical": "Electrical",
    "chemical": "Chemical",
    "fire": "Fire",
    "other": "Other"
  };

  return categoryMap[category] || category;
};
