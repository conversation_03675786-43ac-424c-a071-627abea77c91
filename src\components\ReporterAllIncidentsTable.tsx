import React, { useState, useEffect } from "react";
import { Search, Filter, X, Eye, FileUp, ClipboardList, ArrowUpDown, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Incident } from "@/contexts/IncidentContext";
import { format } from "date-fns";
import AttachmentUpload from "./AttachmentUpload";

interface ReporterAllIncidentsTableProps {
  data: Incident[];
  onView: (incident: Incident) => void;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onAttachmentChange?: (id: string, attachments: File[]) => void;
}

const ReporterAllIncidentsTable: React.FC<ReporterAllIncidentsTableProps> = ({
  data,
  onView,
  onEdit,
  onDelete,
  onAttachmentChange,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState<Incident[]>(data);
  const [typeFilters, setTypeFilters] = useState<string[]>([]);
  const [impactFilters, setImpactFilters] = useState<string[]>([]);
  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [ownerFilters, setOwnerFilters] = useState<string[]>([]);
  const [reportedByFilters, setReportedByFilters] = useState<string[]>([]);
  const [reviewedByFilters, setReviewedByFilters] = useState<string[]>([]);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'ascending' | 'descending' } | null>(null);

  // Get unique values for filters
  const incidentTypes = Array.from(new Set(data.map(incident => incident.incidentType)));
  const impactClassifications = Array.from(new Set(data.map(incident => getImpactClassification(incident))));
  const statuses = Array.from(new Set(data.map(incident => incident.status)));
  const owners = Array.from(new Set(data.map(incident => incident.incidentOwner || "Not Assigned")));
  const reportedByUsers = Array.from(new Set(data.map(incident => incident.reportedBy)));
  const reviewedByUsers = Array.from(new Set(data.map(incident => incident.reviewedBy || "Not Reviewed")));

  // Helper function to get impact classification based on injury classification
  function getImpactClassification(incident: Incident): string {
    const { injuryClassification } = incident;

    if (!injuryClassification) return "Not Classified";

    if (injuryClassification.isFatality === true) {
      return "Level 5 - Fatal";
    } else if (injuryClassification.isPermanentDisability === true) {
      return "Level 4 - Permanent Disability";
    } else if (injuryClassification.isLostTimeIncident === true) {
      return "Level 3 - LTI";
    } else if (injuryClassification.isMedicalTreatment === true) {
      return "Level 2 - MTI";
    } else if (injuryClassification.isFirstAid === true) {
      return "Level 1 - FAI";
    } else if (
      injuryClassification.isFatality === false &&
      injuryClassification.isPermanentDisability === false &&
      injuryClassification.isLostTimeIncident === false &&
      injuryClassification.isMedicalTreatment === false &&
      injuryClassification.isFirstAid === false
    ) {
      return "NEAR MISS";
    }

    return "Not Classified";
  }

  // Update filtered data when search term or filters change
  useEffect(() => {
    console.log("ReporterAllIncidentsTable - Data received:", data);
    let result = [...data];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(incident =>
        incident.id.toLowerCase().includes(term) ||
        incident.description.toLowerCase().includes(term)
      );
    }

    // Apply incident type filters
    if (typeFilters.length > 0) {
      result = result.filter(incident => typeFilters.includes(incident.incidentType));
    }

    // Apply impact classification filters
    if (impactFilters.length > 0) {
      result = result.filter(incident =>
        impactFilters.includes(getImpactClassification(incident))
      );
    }

    // Apply status filters
    if (statusFilters.length > 0) {
      result = result.filter(incident => statusFilters.includes(incident.status));
    }

    // Apply owner filters
    if (ownerFilters.length > 0) {
      result = result.filter(incident =>
        ownerFilters.includes(incident.incidentOwner || "Not Assigned")
      );
    }

    // Apply reported by filters
    if (reportedByFilters.length > 0) {
      result = result.filter(incident =>
        reportedByFilters.includes(incident.reportedBy)
      );
    }

    // Apply reviewed by filters
    if (reviewedByFilters.length > 0) {
      result = result.filter(incident =>
        reviewedByFilters.includes(incident.reviewedBy || "Not Reviewed")
      );
    }

    // Apply sorting
    if (sortConfig !== null) {
      result.sort((a, b) => {
        if (sortConfig.key === 'id') {
          return sortConfig.direction === 'ascending'
            ? a.id.localeCompare(b.id)
            : b.id.localeCompare(a.id);
        } else if (sortConfig.key === 'incidentDate') {
          return sortConfig.direction === 'ascending'
            ? a.incidentDate.getTime() - b.incidentDate.getTime()
            : b.incidentDate.getTime() - a.incidentDate.getTime();
        }
        return 0;
      });
    }

    setFilteredData(result);
  }, [
    data,
    searchTerm,
    typeFilters,
    impactFilters,
    statusFilters,
    ownerFilters,
    reportedByFilters,
    reviewedByFilters,
    sortConfig
  ]);

  // Toggle filter functions
  const toggleTypeFilter = (type: string) => {
    setTypeFilters(prev =>
      prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  };

  const toggleImpactFilter = (impact: string) => {
    setImpactFilters(prev =>
      prev.includes(impact)
        ? prev.filter(i => i !== impact)
        : [...prev, impact]
    );
  };

  const toggleStatusFilter = (status: string) => {
    setStatusFilters(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  const toggleOwnerFilter = (owner: string) => {
    setOwnerFilters(prev =>
      prev.includes(owner)
        ? prev.filter(o => o !== owner)
        : [...prev, owner]
    );
  };

  const toggleReportedByFilter = (reporter: string) => {
    setReportedByFilters(prev =>
      prev.includes(reporter)
        ? prev.filter(r => r !== reporter)
        : [...prev, reporter]
    );
  };

  const toggleReviewedByFilter = (reviewer: string) => {
    setReviewedByFilters(prev =>
      prev.includes(reviewer)
        ? prev.filter(r => r !== reviewer)
        : [...prev, reviewer]
    );
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm("");
    setTypeFilters([]);
    setImpactFilters([]);
    setStatusFilters([]);
    setOwnerFilters([]);
    setReportedByFilters([]);
    setReviewedByFilters([]);
    setSortConfig(null);
  };

  // Handle sorting
  const requestSort = (key: string) => {
    let direction: 'ascending' | 'descending' = 'ascending';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  // Get sort direction indicator
  const getSortDirectionIndicator = (key: string) => {
    if (!sortConfig || sortConfig.key !== key) {
      return null;
    }
    return sortConfig.direction === 'ascending' ? '↑' : '↓';
  };

  // Helper function to get incident category label
  const getIncidentCategoryLabel = (category: string): string => {
    const categoryMap: Record<string, string> = {
      "safety": "Safety",
      "environmental": "Environmental",
      "security": "Security",
      "quality": "Quality",
      "health": "Health",
      "fire": "Fire",
      "electrical": "Electrical",
      "chemical": "Chemical",
      "vehicle": "Vehicle",
      "machinery": "Machinery",
      "other": "Other"
    };
    return categoryMap[category] || category;
  };

  // Helper function to get incident type label
  const getIncidentTypeLabel = (type: string): string => {
    const typeMap: Record<string, string> = {
      "safety": "Safety",
      "environmental": "Environmental",
      "health": "Health",
      "nearMiss": "Near Miss",
      "propertyDamage": "Property Damage",
      "security": "Security",
      "quality": "Quality",
      "other": "Other"
    };
    return typeMap[type] || type;
  };

  // Helper function to get status label
  const getStatusLabel = (status: string): string => {
    const statusMap: Record<string, string> = {
      "draft": "Draft",
      "submitted": "Reported",
      "under-review": "Under Review",
      "investigation": "Under Investigation",
      "closed": "Closed"
    };
    return statusMap[status] || status;
  };

  // Render impact classification badge
  const renderImpactClassification = (incident: Incident) => {
    const classification = getImpactClassification(incident);

    if (classification.includes("Level 5")) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          {classification}
        </span>
      );
    } else if (classification.includes("Level 4")) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
          {classification}
        </span>
      );
    } else if (classification.includes("Level 3")) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          {classification}
        </span>
      );
    } else if (classification.includes("Level 2")) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {classification}
        </span>
      );
    } else if (classification.includes("Level 1")) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          {classification}
        </span>
      );
    } else if (classification.includes("NEAR MISS")) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          {classification}
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          {classification}
        </span>
      );
    }
  };

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "draft":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Draft
          </span>
        );
      case "submitted":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
            Reported
          </span>
        );
      case "under-review":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
            Under Review
          </span>
        );
      case "investigation":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Under Investigation
          </span>
        );
      case "closed":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            Closed
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  // Helper function to determine workflow stage based on status
  const determineWorkflowStage = (status: string): string => {
    switch (status) {
      case 'draft':
        return 'Initial Report';
      case 'submitted':
        return 'Preliminary Analysis in Progress';
      case 'under-review':
        return 'Supplementary information in Progress';
      case 'investigation':
        return 'Investigation';
      case 'closed':
        return 'Preliminary Analysis Completed';
      default:
        return 'Initial Report';
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start mb-6">
        <h2 className="text-xl font-semibold">All Incidents</h2>

        <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
          <div className="relative w-full sm:w-72">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search incidents..."
              className="w-full pl-9 h-10 bg-background border-muted"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-10 w-10 hover:bg-transparent"
                onClick={() => setSearchTerm("")}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {(searchTerm || typeFilters.length > 0 || impactFilters.length > 0 ||
           statusFilters.length > 0 || ownerFilters.length > 0 ||
           reportedByFilters.length > 0 || reviewedByFilters.length > 0) && (
            <Button variant="ghost" onClick={clearFilters} className="h-10">
              Clear Filters
            </Button>
          )}
        </div>
      </div>

      <div className="rounded-md border bg-card overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader className="bg-muted/50">
              <TableRow>
                <TableHead className="w-[100px]">
                  <div className="flex items-center cursor-pointer" onClick={() => requestSort('id')}>
                    ID {getSortDirectionIndicator('id')}
                    <ArrowUpDown className="ml-1 h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead className="w-[150px]">
                  <div className="flex items-center cursor-pointer" onClick={() => requestSort('incidentDate')}>
                    Incident Date {getSortDirectionIndicator('incidentDate')}
                    <ArrowUpDown className="ml-1 h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead className="w-[200px]">Incident Title</TableHead>
                <TableHead className="w-[120px]">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 flex items-center gap-1 -ml-3 font-semibold">
                        Category
                        <Filter className="h-4 w-4" />
                        {typeFilters.length > 0 && (
                          <Badge variant="secondary" className="ml-1 rounded-full px-1">
                            {typeFilters.length}
                          </Badge>
                        )}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-56">
                      <DropdownMenuLabel>Filter by Incident Type</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {incidentTypes.map((type) => (
                        <DropdownMenuCheckboxItem
                          key={type}
                          checked={typeFilters.includes(type)}
                          onCheckedChange={() => toggleTypeFilter(type)}
                        >
                          {getIncidentTypeLabel(type)}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableHead>
                <TableHead className="w-[150px]">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 flex items-center gap-1 -ml-3 font-semibold">
                        Impact Classification
                        <Filter className="h-4 w-4" />
                        {impactFilters.length > 0 && (
                          <Badge variant="secondary" className="ml-1 rounded-full px-1">
                            {impactFilters.length}
                          </Badge>
                        )}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-56">
                      <DropdownMenuLabel>Filter by Impact</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {impactClassifications.map((impact) => (
                        <DropdownMenuCheckboxItem
                          key={impact}
                          checked={impactFilters.includes(impact)}
                          onCheckedChange={() => toggleImpactFilter(impact)}
                        >
                          {impact}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableHead>
                <TableHead className="w-[120px]">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 flex items-center gap-1 -ml-3 font-semibold">
                        Incident Status
                        <Filter className="h-4 w-4" />
                        {statusFilters.length > 0 && (
                          <Badge variant="secondary" className="ml-1 rounded-full px-1">
                            {statusFilters.length}
                          </Badge>
                        )}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-56">
                      <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {statuses.map((status) => (
                        <DropdownMenuCheckboxItem
                          key={status}
                          checked={statusFilters.includes(status)}
                          onCheckedChange={() => toggleStatusFilter(status)}
                        >
                          {getStatusLabel(status)}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableHead>
                <TableHead className="w-[120px]">
                  <Button variant="ghost" size="sm" className="h-8 flex items-center gap-1 -ml-3 font-semibold">
                    Stage
                  </Button>
                </TableHead>
                <TableHead className="w-[150px]">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 flex items-center gap-1 -ml-3 font-semibold">
                        Incident Owner
                        <Filter className="h-4 w-4" />
                        {ownerFilters.length > 0 && (
                          <Badge variant="secondary" className="ml-1 rounded-full px-1">
                            {ownerFilters.length}
                          </Badge>
                        )}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-56">
                      <DropdownMenuLabel>Filter by Owner</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {owners.map((owner) => (
                        <DropdownMenuCheckboxItem
                          key={owner}
                          checked={ownerFilters.includes(owner)}
                          onCheckedChange={() => toggleOwnerFilter(owner)}
                        >
                          {owner}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableHead>
                <TableHead className="w-[150px]">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 flex items-center gap-1 -ml-3 font-semibold">
                        Reported By
                        <Filter className="h-4 w-4" />
                        {reportedByFilters.length > 0 && (
                          <Badge variant="secondary" className="ml-1 rounded-full px-1">
                            {reportedByFilters.length}
                          </Badge>
                        )}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-56">
                      <DropdownMenuLabel>Filter by Reporter</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {reportedByUsers.map((reporter) => (
                        <DropdownMenuCheckboxItem
                          key={reporter}
                          checked={reportedByFilters.includes(reporter)}
                          onCheckedChange={() => toggleReportedByFilter(reporter)}
                        >
                          {reporter}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableHead>
                <TableHead className="w-[150px]">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 flex items-center gap-1 -ml-3 font-semibold">
                        Reviewed By
                        <Filter className="h-4 w-4" />
                        {reviewedByFilters.length > 0 && (
                          <Badge variant="secondary" className="ml-1 rounded-full px-1">
                            {reviewedByFilters.length}
                          </Badge>
                        )}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-56">
                      <DropdownMenuLabel>Filter by Reviewer</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {reviewedByUsers.map((reviewer) => (
                        <DropdownMenuCheckboxItem
                          key={reviewer}
                          checked={reviewedByFilters.includes(reviewer)}
                          onCheckedChange={() => toggleReviewedByFilter(reviewer)}
                        >
                          {reviewer}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableHead>
                <TableHead className="w-[120px] text-center">Actions Taken</TableHead>
                <TableHead className="w-[120px] text-center">Attachment</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.length > 0 ? (
                filteredData.map((incident) => (
                  <TableRow key={incident.id} className="hover:bg-muted/50">
                    <TableCell className="font-medium">
                      <Button
                        variant="link"
                        className="p-0 h-auto font-medium text-primary hover:text-primary/80"
                        onClick={() => onView(incident)}
                      >
                        {incident.id}
                      </Button>
                    </TableCell>
                    <TableCell>{format(incident.incidentDate, "do MMM yyyy")}</TableCell>
                    <TableCell>
                      <div className="max-w-[180px] truncate font-medium" title={incident.incidentTitle || incident.description}>
                        {incident.incidentTitle || incident.description}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {incident.locationBusinessUnit && `at ${incident.locationBusinessUnit}`}
                        {incident.locationDetails && ` - ${incident.locationDetails}`}
                      </div>
                    </TableCell>
                    <TableCell>{getIncidentTypeLabel(incident.incidentType)}</TableCell>
                    <TableCell>
                      {renderImpactClassification(incident)}
                    </TableCell>
                    <TableCell>
                      {renderStatusBadge(incident.status)}
                    </TableCell>
                    <TableCell>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        {incident.stage ? incident.stage : determineWorkflowStage(incident.status)}
                      </span>
                    </TableCell>
                    <TableCell>{incident.incidentOwner || "Not Assigned"}</TableCell>
                    <TableCell>{incident.reportedBy}</TableCell>
                    <TableCell>{incident.reviewedBy || "Not Reviewed"}</TableCell>
                    <TableCell className="text-center">
                      <span className="inline-flex items-center justify-center w-8 h-8 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        0/0
                      </span>
                    </TableCell>
                    <TableCell className="text-center">
                      <AttachmentUpload
                        incidentId={incident.id}
                        existingAttachments={incident.attachments?.map(url => {
                          // Create a File object from the URL
                          // In a real app with a backend, you'd fetch the actual file
                          // For now, we'll create a dummy file with the URL as the name to preserve it
                          const dummyFile = new File([""], url, {
                            type: "image/jpeg",
                            lastModified: new Date().getTime()
                          });
                          // Store the URL in the file name for retrieval
                          Object.defineProperty(dummyFile, 'name', {
                            writable: true,
                            value: url
                          });
                          return dummyFile;
                        }) || []}
                        onAttachmentChange={
                          onAttachmentChange
                            ? (attachments) => onAttachmentChange(incident.id, attachments)
                            : undefined
                        }
                      />
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={12} className="text-center py-8 text-gray-500">
                    <div className="flex flex-col items-center justify-center text-muted-foreground">
                      <AlertCircle className="h-8 w-8 mb-2 opacity-40" />
                      <p>No incidents found.</p>
                      <p className="text-sm">Try adjusting your filters or search criteria.</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default ReporterAllIncidentsTable;
