
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import SimpleLayout from "./components/layout/SimpleLayout";
import Index from "./pages/Index";
import Incidents from "./pages/Incidents";
import NotFound from "./pages/NotFound";
import ReporterDashboard from "./pages/ReporterDashboard";
import ReviewerDashboard from "./pages/ReviewerDashboard";

import { UserProvider } from "./contexts/UserContext";
import { IncidentProvider } from "./contexts/IncidentContext";
import { defaultPath } from "./lib/paths";
import "./App.css";

const queryClient = new QueryClient();

const App = () => (
  <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
    <QueryClientProvider client={queryClient}>
      <UserProvider>
        <IncidentProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <SimpleLayout>
                <Routes>
                  {/* Redirect root to reporter dashboard by default */}
                  <Route path="/" element={<Navigate to={defaultPath.reporter} replace />} />

                  {/* Role-specific dashboards */}
                  <Route path={defaultPath.reporter} element={<ReporterDashboard />} />
                  <Route path={defaultPath.reviewer} element={<ReviewerDashboard />} />

                  {/* Legacy routes */}
                  <Route path={defaultPath.incidents} element={<Incidents />} />
                  <Route path={defaultPath.report} element={<Index />} />

                  {/* Catch-all route */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </SimpleLayout>
            </BrowserRouter>
          </TooltipProvider>
        </IncidentProvider>
      </UserProvider>
    </QueryClientProvider>
  </ThemeProvider>
);

export default App;
